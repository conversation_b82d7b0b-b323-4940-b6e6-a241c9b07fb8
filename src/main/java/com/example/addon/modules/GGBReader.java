package com.example.addon.modules;

import com.example.addon.BaseModule;
import com.example.addon.utils.ConstantPoolDumper;

public class GGBReader extends BaseModule {
    public GGBReader() {
        super("GGBReader", "read");
    }


    @Override
    public void onActivate() {
//        BlockUtilGrimConstantsReader.read();
        ConstantPoolDumper.dumpConstantPool("meteordevelopment.meteorclient", "D:/map.json");
        this.toggle();
    }

}
